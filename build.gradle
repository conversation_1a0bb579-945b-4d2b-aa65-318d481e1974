plugins {
    id 'java'
    id 'maven-publish'
    id('com.github.hierynomus.license') version '0.14.0'
    id('io.github.spencerpark.jupyter-kernel-installer') version '2.1.0'
    id('com.github.jk1.dependency-license-report')
}

import org.apache.tools.ant.filters.ReplaceTokens
import com.github.jk1.license.render.*
import com.github.jk1.license.filter.*
import io.github.spencerpark.gradle.*

group = 'io.github.spencerpark'
version = '1.3.0'

wrapper {
    gradleVersion = '4.8.1'
    distributionType = Wrapper.DistributionType.ALL
}

// Add the license header to source files
license {
    header = file('LICENSE')
    exclude '**/*.json'
    mapping {
        // Use a regular multiline comment rather than a javadoc comment
        java = 'SLASHSTAR_STYLE'
    }
}
build.dependsOn 'licenseFormat'

// Configures the license report generated for the dependencies.
licenseReport {
    excludeGroups = []
    renderers = [
            // Generate a pretty HTML report that groups dependencies by their license.
            new NewInventoryHtmlReportRenderer('dependencies.html'),
            // TODO make sure ci verifies that all licenses are know to be allowed to redistribute before publishing
            new JsonReportRenderer('dependencies.json')
    ]

    // Group same licenses despite names being slightly different (ex. Apache 2.0 vs Apache version 2)
    filters = [new LicenseBundleNormalizer()]

    configurations = ['compile']
}

compileJava {
    sourceCompatibility = 1.9
    targetCompatibility = 1.9
}

configurations {
    shade
    // transitive true to make sure that the dependencies of shade dependencies also get shaded
    // into the jar
    shade.transitive = true
    compile.extendsFrom(shade)
}

repositories {
    mavenCentral()
    maven {
        url = 'https://oss.sonatype.org/content/repositories/snapshots/'
    }
    mavenLocal()
}

dependencies {
    shade group: 'io.github.spencerpark', name: 'jupyter-jvm-basekernel', version: '2.3.0'

    shade group: 'org.apache.ivy', name: 'ivy', version: '2.5.0-rc1'
    //shade group: 'org.apache.maven', name: 'maven-settings-builder', version: '3.6.0'
    shade group: 'org.apache.maven', name: 'maven-model-builder', version: '3.6.0'

    testCompile group: 'junit', name: 'junit', version: '4.12'
}

jar {
    //Include all shaded dependencies in the jar
    from configurations.shade
            .collect { it.isDirectory() ? it : zipTree(it) }

    manifest {
        attributes('Main-class': 'io.github.spencerpark.ijava.IJava')
    }
}

processResources {
    def tokens = [
            'version': project.version,
            'project': project.name
    ]
    inputs.properties(tokens)
    filter ReplaceTokens, tokens: tokens
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
}

jupyter {
    kernelName = 'java'
    kernelDisplayName = 'Java'
    kernelLanguage = 'java'
    kernelInterruptMode = 'message'

    kernelParameters {
        list('classpath', 'IJAVA_CLASSPATH') {
            separator = PATH_SEPARATOR
            description = '''A file path separator delimited list of classpath entries that should be available to the user code. **Important:** no matter what OS, this should use forward slash "/" as the file separator. Also each path may actually be a simple glob.'''
        }

        list('comp-opts', 'IJAVA_COMPILER_OPTS') {
            separator = ' '
            description = '''A space delimited list of command line options that would be passed to the `javac` command when compiling a project. For example `-parameters` to enable retaining parameter names for reflection.'''
        }

        list('startup-scripts-path', 'IJAVA_STARTUP_SCRIPTS_PATH') {
            separator = PATH_SEPARATOR
            description = '''A file path seperator delimited list of `.jshell` scripts to run on startup. This includes ijava-jshell-init.jshell and ijava-display-init.jshell. **Important:** no matter what OS, this should use forward slash "/" as the file separator. Also each path may actually be a simple glob.'''
        }

        string('startup-script', 'IJAVA_STARTUP_SCRIPT') {
            description = '''A block of java code to run when the kernel starts up. This may be something like `import my.utils;` to setup some default imports or even `void sleep(long time) { try {Thread.sleep(time); } catch (InterruptedException e) { throw new RuntimeException(e); }}` to declare a default utility method to use in the notebook.'''
        }

        string('timeout', 'IJAVA_TIMEOUT') {
            aliases NO_TIMEOUT: '-1'
            description = '''A duration specifying a timeout (in milliseconds by default) for a _single top level statement_. If less than `1` then there is no timeout. If desired a time may be specified with a `TimeUnit` may be given following the duration number (ex `"30 SECONDS"`).'''
        }
    }
}

installKernel {
    kernelInstallPath = commandLineSpecifiedPath(userInstallPath)
}

zipKernel {
    installers {
        with 'python'
    }

    from(generateLicenseReport.outputFolder) {
        into 'dependency-licenses'
    }
}
zipKernel.dependsOn 'generateLicenseReport'